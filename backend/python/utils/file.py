from enum import Enum
from hashlib import sha256
from functools import lru_cache
from pydantic import BaseModel, Field
from pathlib import Path
from typing import List, Optional, Union, Dict
from collections import deque
import fnmatch

from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

class FileType(Enum):
    CODE = "code"
    DOC = "doc"
    
    @classmethod
    def from_suffix(cls, suffix: str):
        if suffix in [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp", ".go", ".rs", ".php", ".rb"]:
            return cls.CODE
        else:
            return cls.DOC
        
class FileNode(BaseModel):
    id: str = Field(default_factory=str)
    name: str = Field(default_factory=str)
    type: str = Field(default_factory=str)  # 'file' or 'directory'
    path: str = Field(default_factory=str)
    size: Optional[int] = Field(default=0)
    lastModified: Optional[str] = Field(default="")
    children: Optional[List['FileNode']] = Field(default_factory=list)

    def __str__(self):
        """返回文件树的树形字符串表示"""
        return self._to_tree_string()

    def _to_tree_string(self, prefix: str = "", is_last: bool = True) -> str:
        """
        递归生成树形字符串表示

        Args:
            prefix: 当前行的前缀字符串
            is_last: 是否是同级中的最后一个节点

        Returns:
            树形字符串
        """
        # 当前节点的符号
        current_symbol = "└── " if is_last else "├── "

        # 当前节点的完整行
        result = prefix + current_symbol + self.name + "\n"

        # 如果有子节点，递归处理
        if self.children:
            # 为子节点准备前缀
            child_prefix = prefix + ("    " if is_last else "│   ")

            for i, child in enumerate(self.children):
                is_child_last = (i == len(self.children) - 1)
                result += child._to_tree_string(child_prefix, is_child_last)

        return result
    
# 工具函数
def get_file_size(file_path: Path) -> int:
    """获取文件大小"""
    try:
        return file_path.stat().st_size
    except:
        return 0

def get_last_modified(file_path: Path) -> str:
    """获取文件最后修改时间"""
    try:
        return file_path.stat().st_mtime.__str__()
    except:
        return ""

def get_file_filter_config():
    """获取文件过滤配置"""
    try:
        config = get_config()
        return {
            'exclude': config.file_filter.exclude,
            'include': config.file_filter.include,
            'max_file_size': config.file_filter.max_file_size
        }
    except Exception:
        # 配置加载失败时使用默认配置
        return {
            'exclude': ['.git', '.svn', '.hg', 'node_modules', '__pycache__', '.pytest_cache',
                       'target', 'build', 'dist', '.next', '.vscode', '.idea', '.DS_Store',
                       '*.pyc', '*.pyo', '*.pyd', '.venv'],
            'include': [".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".hpp",
                       ".go", ".rs", ".php", ".rb", ".md"],
            'max_file_size': 1048576  # 1MB
        }


def should_ignore_path(path: Union[Path | str]) -> bool:
    """根据配置文件中的设置判断是否应该忽略某个路径"""
    filter_config = get_file_filter_config()
    exclude_patterns = filter_config['exclude']
    include_extensions = filter_config['include']
    max_file_size = filter_config['max_file_size']

    if isinstance(path, str):
        path = Path(path)
    name = path.name

    # 检查排除模式
    for pattern in exclude_patterns:
        if '*' in pattern:
            # 处理通配符模式，如 *.pyc
            if fnmatch.fnmatch(name, pattern):
                return True
        else:
            # 处理精确匹配或目录名
            if name == pattern:
                logger.info(f"Exclude path: {path} by pattern: {pattern}")
                return True

    # 如果是文件，检查扩展名和大小
    if path.is_file():
        # 检查文件扩展名是否在包含列表中
        file_extension = path.suffix.lower()
        if include_extensions and file_extension not in include_extensions:
            return True

        # 检查文件大小
        try:
            if path.stat().st_size > max_file_size:
                return True
        except (OSError, PermissionError):
            # 如果无法获取文件大小，默认不忽略
            pass

    return False

@lru_cache(maxsize=128)
def build_file_tree(root_dir: Union[str | Path], dir_path: Union[str | Path], max_leaf_nodes: int = 1000, max_depth: int = 50) -> List[FileNode]:
    """
    构建文件树，使用宽度优先搜索策略

    Args:
        root_dir: 根目录路径
        dir_path: 当前目录路径
        max_leaf_nodes: 最大叶子节点数量，用于控制遍历复杂度
        max_depth: 最大深度，作为安全边界

    Returns:
        List[FileNode]: 文件树节点列表
    """
    if isinstance(root_dir, str):
        root_dir = Path(root_dir)

    if isinstance(dir_path, str):
        dir_path = Path(dir_path)

    # 如果起始目录就是根目录，直接使用宽度优先搜索
    if root_dir == dir_path:
        return _build_file_tree_bfs(root_dir, max_leaf_nodes, max_depth)
    else:
        # 对于子目录调用，保持原有的递归逻辑以兼容现有代码
        return _build_file_tree_recursive(root_dir, dir_path, max_depth, 0)

def _build_file_tree_bfs(root_dir: Path, max_leaf_nodes: int, max_depth: int) -> List[FileNode]:
    """
    使用宽度优先搜索构建文件树

    Args:
        root_dir: 根目录路径
        max_leaf_nodes: 最大叶子节点数量
        max_depth: 最大深度

    Returns:
        List[FileNode]: 文件树节点列表
    """
    # 存储结果的根节点列表
    root_nodes = []

    # BFS队列：(目录路径, 父节点, 当前深度)
    queue = deque([(root_dir, None, 0)])

    # 记录当前叶子节点数量
    leaf_count = 0

    # 自定义排序函数：目录在前，文件在后，然后按名称字母顺序排序
    def sort_key(item):
        return (0 if item.is_dir() else 1, item.name.lower())

    while queue and leaf_count < max_leaf_nodes:
        current_dir, parent_node, depth = queue.popleft()

        # 检查深度限制
        if depth >= max_depth:
            continue

        try:
            # 获取当前目录下的所有项目并排序
            items = sorted([item for item in current_dir.iterdir() if not should_ignore_path(item)], key=sort_key)

            # 分离目录和文件
            directories = [item for item in items if item.is_dir()]
            files = [item for item in items if item.is_file()]

            # 处理文件（叶子节点）
            for file_item in files:
                if leaf_count >= max_leaf_nodes:
                    break

                relative_path = file_item.relative_to(root_dir)
                file_node = FileNode(
                    id=str(relative_path),
                    name=file_item.name,
                    type="file",
                    path=str(relative_path),
                    size=get_file_size(file_item),
                    lastModified=get_last_modified(file_item)
                )

                if parent_node is None:
                    root_nodes.append(file_node)
                else:
                    parent_node.children.append(file_node)

                leaf_count += 1

            # 处理目录
            for dir_item in directories:
                if leaf_count >= max_leaf_nodes:
                    break

                relative_path = dir_item.relative_to(root_dir)
                dir_node = FileNode(
                    id=str(relative_path),
                    name=dir_item.name,
                    type="directory",
                    path=str(relative_path),
                    children=[]
                )

                if parent_node is None:
                    root_nodes.append(dir_node)
                else:
                    parent_node.children.append(dir_node)

                # 检查是否应该继续遍历这个目录
                should_traverse = _should_traverse_directory(dir_item, depth, max_depth, leaf_count, max_leaf_nodes)

                if should_traverse:
                    queue.append((dir_item, dir_node, depth + 1))
                else:
                    # 如果不继续遍历，这个目录就是叶子节点
                    leaf_count += 1

        except PermissionError:
            # 权限错误时跳过该目录
            continue

    return root_nodes

def _should_traverse_directory(dir_path: Path, current_depth: int, max_depth: int, current_leaf_count: int, max_leaf_nodes: int) -> bool:
    """
    判断是否应该继续遍历目录

    Args:
        dir_path: 目录路径
        current_depth: 当前深度
        max_depth: 最大深度
        current_leaf_count: 当前叶子节点数量
        max_leaf_nodes: 最大叶子节点数量

    Returns:
        bool: 是否应该继续遍历
    """
    # 检查深度限制
    if current_depth >= max_depth - 1:
        return False

    # 检查叶子节点数量限制
    if current_leaf_count >= max_leaf_nodes:
        return False

    try:
        # 获取目录下的非忽略项目
        items = [item for item in dir_path.iterdir() if not should_ignore_path(item)]

        # 如果目录为空，不需要遍历
        if not items:
            return False

        # 分离目录和文件
        directories = [item for item in items if item.is_dir()]
        files = [item for item in items if item.is_file()]

        # 特殊规则：如果只有一个子目录且没有文件，继续遍历
        if len(directories) == 1 and len(files) == 0:
            return True

        # 如果有文件或多个目录，根据剩余容量决定
        remaining_capacity = max_leaf_nodes - current_leaf_count
        total_items = len(files) + len(directories)

        # 如果剩余容量足够，继续遍历
        return total_items <= remaining_capacity

    except PermissionError:
        return False

def _build_file_tree_recursive(root_dir: Path, dir_path: Path, max_depth: int, current_depth: int) -> List[FileNode]:
    """
    递归构建文件树（保持向后兼容）

    Args:
        root_dir: 根目录路径
        dir_path: 当前目录路径
        max_depth: 最大深度
        current_depth: 当前深度

    Returns:
        List[FileNode]: 文件树节点列表
    """
    if current_depth >= max_depth:
        return []

    nodes = []
    try:
        # 自定义排序：目录在前，文件在后，然后按名称字母顺序排序
        def sort_key(item):
            return (0 if item.is_dir() else 1, item.name.lower())

        for item in sorted(dir_path.iterdir(), key=sort_key):
            if should_ignore_path(item):
                continue

            relative_path = item.relative_to(root_dir)

            node = FileNode(
                id=str(relative_path),
                name=item.name,
                type="directory" if item.is_dir() else "file",
                path=str(relative_path),
            )

            if item.is_file():
                node.size = get_file_size(item)
                node.lastModified = get_last_modified(item)
            elif item.is_dir():
                # 递归获取子目录
                node.children = _build_file_tree_recursive(
                    root_dir, item, max_depth, current_depth + 1
                )

            nodes.append(node)
    except PermissionError:
        pass

    return nodes

def build_file_list(dir_path: Union[str | Path]) -> Dict[str, str]:
    """
    递归读取project_dir中的文件，忽略ignores中的目录或文件
    在读取文件时，在每行的文件前加上行号信息，如 l:{line num} | {code}
    返回文件路径到内容的映射
    """
    doc_content = {}
    if isinstance(dir_path, str):
        dir_path = Path(dir_path)
    
    file_nodes = build_file_tree(dir_path, dir_path, max_leaf_nodes=5000, max_depth=100)

    def traverse_and_read(node: FileNode):
        if node.type == "file":
            try:
                with open(node.path, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    doc_content[node.path] = "\n".join(lines)
            except Exception as e:
                logger.error(f"Error reading file {node.path}: {e}")
        else:
            for child in node.children:
                traverse_and_read(child)
    
    for node in file_nodes:
        traverse_and_read(node)
    
    return doc_content


def generate_file_hash_name(file_path: Union[str | Path], hash_len: str = 32):
    if isinstance(file_path, Path):
        file_path = str(file_path)
    
    assert len(file_path) > 0 and hash_len < 256

    return sha256(file_path.encode()).hexdigest()[:hash_len]