import os
import re
from typing import List, Tuple, Optional
import subprocess
import xml.etree.ElementTree as ET
import fnmatch
import asyncio

from modules.common.schema import CodeSnippet
from modules.integration.tools.search.search_tool import SearchToolABC
from core.config import get_config
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)

from utils.file import should_ignore_path

class GrepSearchTool(SearchToolABC):
    """基于grep的搜索引擎"""
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.file_filter = get_config().file_filter

    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用grep搜索代码片段，支持XML格式的查询参数

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 构建grep命令
            cmd = self._build_grep_command(search_params)

            # 执行搜索
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                snippets = self._parse_grep_output(result.stdout)
            elif result.returncode == 1:
                # grep返回1表示没有找到匹配，这是正常情况
                logger.info("No matches found")
            else:
                logger.warning(f"Grep command failed with return code {result.returncode}: {result.stderr}")

        except Exception as e:
            logger.error(f"Grep search failed: {e}")

        return snippets

    async def search_async(self, query: str, **kwargs) -> List[CodeSnippet]:
        """
        使用grep搜索代码片段的异步版本

        Args:
            query: 查询字符串，可以是XML格式或普通文本

        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []

        try:
            # 解析查询参数
            search_params = self._parse_query(query)

            # 构建grep命令
            cmd = self._build_grep_command(search_params)

            # 异步执行搜索
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                encoding='utf-8',
                errors='ignore'
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                snippets = self._parse_grep_output(stdout)
            elif process.returncode == 1:
                # grep返回1表示没有找到匹配，这是正常情况
                logger.info("No matches found")
            else:
                logger.warning(f"Grep command failed with return code {process.returncode}: {stderr}")

        except Exception as e:
            logger.error(f"Async grep search failed: {e}")

        return snippets

    def _parse_query(self, query: str) -> dict:
        """
        解析查询参数，支持XML格式和普通文本

        Args:
            query: 查询字符串

        Returns:
            dict: 解析后的参数字典
        """
        # 默认参数
        params = {
            'path': self.repo_path,
            'regex': query.strip(),
            'file_pattern': None
        }

        # 尝试解析XML格式
        if query.strip().startswith('<grep>'):
            try:
                # 解析XML
                root = ET.fromstring(query.strip())

                # 提取参数
                path_elem = root.find('path')
                if path_elem is not None and path_elem.text:
                    # 处理相对路径
                    path = path_elem.text.strip()
                    if path == '.':
                        params['path'] = self.repo_path
                    elif os.path.isabs(path):
                        params['path'] = path
                    else:
                        params['path'] = os.path.join(self.repo_path, path)

                regex_elem = root.find('regex')
                if regex_elem is not None and regex_elem.text:
                    params['regex'] = regex_elem.text.strip()

                file_pattern_elem = root.find('file_pattern')
                if file_pattern_elem is not None and file_pattern_elem.text:
                    params['file_pattern'] = file_pattern_elem.text.strip()

            except ET.ParseError as e:
                logger.warning(f"Failed to parse XML query, using as plain text: {e}")

        return params

    def _build_grep_command(self, params: dict) -> List[str]:
        """
        构建grep命令

        Args:
            params: 搜索参数

        Returns:
            List[str]: grep命令参数列表
        """
        cmd = [
            "grep",
            "-r",  # 递归搜索
            "-n",  # 显示行号
            "-E",  # 使用扩展正则表达式
            "-C", "3",  # 显示上下文3行
        ]

        # 处理正则表达式中的大小写不敏感标志
        regex = params['regex']
        if regex.startswith('(?i)'):
            cmd.append('-i')  # 忽略大小写
            regex = regex[4:]  # 移除(?i)前缀

        cmd.append(regex)
        cmd.append(params['path'])

        # 添加文件模式过滤
        if params['file_pattern']:
            cmd.extend(["--include", params['file_pattern']])
        else:
            # 使用配置文件中的文件类型过滤
            for ext in self.file_filter.include:
                cmd.extend(["--include", f"*{ext}"])

        return cmd

    def _parse_grep_output(self, output: str) -> List[CodeSnippet]:
        """
        解析grep输出结果
        
        Args:
            output: grep命令输出
            
        Returns:
            List[CodeSnippet]: 解析后的代码片段
        """
        snippets = []
        current_file = None
        current_lines = []
        
        for line in output.split('\n'):
            if not line.strip() or line.strip() == '--':
                if current_file and current_lines:
                    snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
                    current_lines = []
                current_file = None
                continue
            
            # 解析grep输出格式：文件名:行号:内容 或 文件名-行号-内容（上下文）
            match = re.match(r'^([^:]+):(\d+):(.*)$', line)
            context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)
            
            if match:
                file_path, line_number, content = match.groups()
                if not should_ignore_path(file_path):
                    current_file = file_path
                    current_lines.append((int(line_number), content, True))  # True表示匹配行
            elif context_match:
                file_path, line_number, content = context_match.groups()
                if current_file == file_path:
                    current_lines.append((int(line_number), content, False))  # False表示上下文行
        
        # 处理最后一组
        if current_file and current_lines:
            snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
        
        return snippets
    
    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """
        从行数据中提取代码片段
        
        Args:
            file_path: 文件路径
            lines: 行数据列表 (行号, 内容, 是否匹配行)
            
        Returns:
            List[CodeSnippet]: 代码片段列表
        """
        snippets = []
        
        # 按匹配行分组
        i = 0
        while i < len(lines):
            line_number, content, is_match = lines[i]
            if is_match:
                # 收集上下文
                context_before = []
                context_after = []
                
                # 向前查找上下文
                j = i - 1
                while j >= 0 and not lines[j][2]:  # 上下文行
                    context_before.insert(0, lines[j][1])
                    j -= 1
                
                # 向后查找上下文
                j = i + 1
                while j < len(lines) and not lines[j][2]:  # 上下文行
                    context_after.append(lines[j][1])
                    j += 1
                
                snippet = CodeSnippet(
                    file_path=os.path.relpath(file_path, self.repo_path),
                    start_line=line_number,
                    end_line=line_number,
                    content=content,
                    context_before="\n".join(context_before),
                    context_after="\n".join(context_after)
                )
                snippets.append(snippet)
            
            i += 1
        
        return snippets

    @property
    def description(self):
        return """-`grep`: Perform a powerful regex-based search across multiple files within a directory tree. This tool recursively searches through files, finds pattern matches, and returns each match with surrounding context lines for better understanding. Ideal for code analysis, finding specific patterns, locating function definitions, or searching for text across large codebases.

Parameters:
- path: (required) The target directory path to search in, relative to the current working directory. The search will recursively include all subdirectories. Use "." for current directory.
- regex: (required) The regular expression pattern to search for. Uses extended grep regex syntax (POSIX ERE). Supports:
  - Basic patterns: `function`, `class MyClass`
  - Case-insensitive: `(?i)pattern` (special prefix handled by tool)
  - Word boundaries: `\\bword\\b`
  - Character classes: `[a-zA-Z]`, `[0-9]`
  - Quantifiers: `+`, `*`, `?`, `{n,m}`
  - Alternation: `pattern1|pattern2`
  - Grouping: `(pattern)`
- file_pattern: (optional) Glob pattern to filter which files to search. Examples:
  - `*.js` - only JavaScript files
  - `*.{ts,tsx}` - TypeScript files
  - `**/*.py` - Python files in all subdirectories
  - If omitted, uses configured file extensions

Note: Results will show each match with surrounding context lines. Large result sets may be truncated."""
    
    @property
    def examples(self):
        return """<output>
    <grep>
    <path>src</path>
    <regex>function\\s+(\\w+)</regex>
    <file_pattern>*.js</file_pattern>
    </grep>

    <grep>
    <path>.</path>
    <regex>(?i)todo|fixme</regex>
    </grep>
</output>
"""